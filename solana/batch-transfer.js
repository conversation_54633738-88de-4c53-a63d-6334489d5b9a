// solTransfers.js
import fs from "fs";
import bs58 from "bs58";
import {
  Connection,
  clusterApiUrl,
  Keypair,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  PublicKey,
} from "@solana/web3.js";
import {
  getOrCreateAssociatedTokenAccount,
  createTransferCheckedInstruction,
  getAccount,
} from "@solana/spl-token";

const connection = new Connection(clusterApiUrl("mainnet-beta"), "confirmed");
const MAIN_WALLET = "DkjL9fNcNXWfu1G1xDsctrn6WXXb3epyphKpyQurAhaU";
const LEAF_ADDRESS = "EWbYEzhuyNm8pZntv1bbHUQtsJCW1esErofEUSyYpump";
const DECIMALS = 6;

const IDL = {
  address: "EsybJRDoqxAnnBBwjvQ42X1xkgokZEKmSP9Y8sZV7bzN",
  metadata: {
    name: "token_staking_program",
    version: "0.1.0",
    spec: "0.1.0",
    description: "Created with Anchor",
  },
  instructions: [
    {
      name: "add_claimant",
      discriminator: [219, 251, 213, 252, 211, 243, 208, 238],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claimant",
        },
        {
          name: "claim",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109],
              },
              {
                kind: "account",
                path: "pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "claim",
      discriminator: [62, 198, 214, 193, 213, 159, 108, 210],
      accounts: [
        {
          name: "claimant",
          writable: !0,
          signer: !0,
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claim",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109],
              },
              {
                kind: "account",
                path: "pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "claimant_ata",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "claimant",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "mint",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [],
    },
    {
      name: "claim_staking_rewards",
      discriminator: [229, 141, 170, 69, 111, 94, 6, 72],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "user",
              },
            ],
          },
        },
        {
          name: "reward_vault",
          writable: !0,
        },
        {
          name: "user_reward_account",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "user",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "reward_mint",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "reward_mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [],
    },
    {
      name: "claim_to_stake",
      discriminator: [196, 151, 171, 133, 183, 192, 205, 198],
      accounts: [
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claim_status",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109, 95, 115, 116, 97, 116, 117, 115],
              },
              {
                kind: "account",
                path: "pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
        },
        {
          name: "claimant",
          writable: !0,
          signer: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [
        {
          name: "allocation",
          type: "u64",
        },
        {
          name: "proof",
          type: {
            vec: {
              array: ["u8", 32],
            },
          },
        },
      ],
    },
    {
      name: "claim_with_proof",
      discriminator: [38, 165, 237, 119, 50, 165, 25, 163],
      accounts: [
        {
          name: "claimant",
          writable: !0,
          signer: !0,
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claim_status",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109, 95, 115, 116, 97, 116, 117, 115],
              },
              {
                kind: "account",
                path: "pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "claimant_ata",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "claimant",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "mint",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [
        {
          name: "allocation",
          type: "u64",
        },
        {
          name: "proof",
          type: {
            vec: {
              array: ["u8", 32],
            },
          },
        },
      ],
    },
    {
      name: "close_pool",
      discriminator: [140, 189, 209, 23, 239, 62, 239, 11],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "rent_collector",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [],
    },
    {
      name: "complete_unstake",
      discriminator: [79, 98, 40, 241, 100, 30, 25, 234],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "user",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
        },
        {
          name: "user_token_account",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "user",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "staking_mint",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "reward_vault",
          writable: !0,
        },
        {
          name: "user_reward_account",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "user",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "reward_mint",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "staking_mint",
        },
        {
          name: "reward_mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [],
    },
    {
      name: "freeze_pool",
      discriminator: [211, 216, 1, 216, 54, 191, 102, 150],
      accounts: [
        {
          name: "authority",
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
      ],
      args: [],
    },
    {
      name: "fund_pool",
      discriminator: [36, 57, 233, 176, 181, 20, 87, 159],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "authority_token_account",
          writable: !0,
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "fund_rewards",
      discriminator: [114, 64, 163, 112, 175, 167, 19, 121],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "reward_vault",
          writable: !0,
        },
        {
          name: "authority_token_account",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "initialize_pool",
      discriminator: [95, 180, 10, 172, 84, 174, 232, 40],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
        },
        {
          name: "mint",
        },
        {
          name: "pool",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [112, 111, 111, 108],
              },
              {
                kind: "account",
                path: "mint",
              },
              {
                kind: "account",
                path: "authority",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [118, 97, 117, 108, 116],
              },
              {
                kind: "account",
                path: "pool",
              },
            ],
          },
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [
        {
          name: "args",
          type: {
            defined: {
              name: "InitializePoolArgs",
            },
          },
        },
      ],
    },
    {
      name: "initialize_staking_pool",
      discriminator: [231, 155, 216, 76, 185, 211, 34, 151],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
        },
        {
          name: "mint",
        },
        {
          name: "reward_mint",
        },
        {
          name: "staking_pool",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [
                  115, 116, 97, 107, 105, 110, 103, 95, 112, 111, 111, 108,
                ],
              },
              {
                kind: "account",
                path: "mint",
              },
              {
                kind: "account",
                path: "authority",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [
                  115, 116, 97, 107, 105, 110, 103, 95, 118, 97, 117, 108, 116,
                ],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
            ],
          },
        },
        {
          name: "reward_vault",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [
                  114, 101, 119, 97, 114, 100, 95, 118, 97, 117, 108, 116,
                ],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
            ],
          },
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [
        {
          name: "args",
          type: {
            defined: {
              name: "InitializeStakingPoolArgs",
            },
          },
        },
      ],
    },
    {
      name: "request_unlock",
      discriminator: [114, 219, 84, 115, 190, 220, 130, 240],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "user",
              },
            ],
          },
        },
      ],
      args: [],
    },
    {
      name: "set_merkle_root",
      discriminator: [43, 24, 91, 60, 240, 137, 28, 102],
      accounts: [
        {
          name: "authority",
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
      ],
      args: [
        {
          name: "merkle_root",
          type: {
            array: ["u8", 32],
          },
        },
        {
          name: "enable",
          type: "bool",
        },
      ],
    },
    {
      name: "stake_tokens",
      discriminator: [136, 126, 91, 162, 40, 131, 13, 127],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "user",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
        },
        {
          name: "user_token_account",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "11111111111111111111111111111111",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "unfreeze_pool",
      discriminator: [236, 22, 34, 179, 44, 68, 15, 108],
      accounts: [
        {
          name: "authority",
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
      ],
      args: [],
    },
    {
      name: "withdraw_unallocated",
      discriminator: [226, 26, 221, 64, 218, 61, 68, 231],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "authority_token_account",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
  ],
  accounts: [
    {
      name: "Claim",
      discriminator: [155, 70, 22, 176, 123, 215, 246, 102],
    },
    {
      name: "ClaimStatus",
      discriminator: [22, 183, 249, 157, 247, 95, 150, 96],
    },
    {
      name: "Pool",
      discriminator: [241, 154, 109, 4, 17, 177, 109, 188],
    },
    {
      name: "StakingPool",
      discriminator: [203, 19, 214, 220, 220, 154, 24, 102],
    },
    {
      name: "UserStake",
      discriminator: [102, 53, 163, 107, 9, 138, 87, 153],
    },
  ],
  errors: [
    {
      code: 6e3,
      name: "InvalidAmount",
      msg: "Invalid amount",
    },
    {
      code: 6001,
      name: "Overflow",
      msg: "Arithmetic overflow",
    },
    {
      code: 6002,
      name: "ExceedsUnclaimed",
      msg: "Withdraw amount exceeds unclaimed balance",
    },
  ],
  types: [
    {
      name: "Claim",
      type: {
        kind: "struct",
        fields: [
          {
            name: "pool",
            type: "pubkey",
          },
          {
            name: "claimant",
            type: "pubkey",
          },
          {
            name: "total_allocation",
            type: "u64",
          },
          {
            name: "claimed_amount",
            type: "u64",
          },
          {
            name: "bump",
            type: "u8",
          },
        ],
      },
    },
    {
      name: "ClaimStatus",
      type: {
        kind: "struct",
        fields: [
          {
            name: "pool",
            type: "pubkey",
          },
          {
            name: "claimant",
            type: "pubkey",
          },
          {
            name: "claimed_amount",
            type: "u64",
          },
          {
            name: "bump",
            type: "u8",
          },
        ],
      },
    },
    {
      name: "InitializePoolArgs",
      type: {
        kind: "struct",
        fields: [
          {
            name: "start_ts",
            type: "i64",
          },
          {
            name: "end_ts",
            type: "i64",
          },
        ],
      },
    },
    {
      name: "InitializeStakingPoolArgs",
      type: {
        kind: "struct",
        fields: [
          {
            name: "reward_rate",
            type: "u64",
          },
          {
            name: "start_time",
            type: "i64",
          },
          {
            name: "end_time",
            type: "i64",
          },
          {
            name: "unlock_duration",
            type: "i64",
          },
        ],
      },
    },
    {
      name: "Pool",
      type: {
        kind: "struct",
        fields: [
          {
            name: "authority",
            type: "pubkey",
          },
          {
            name: "mint",
            type: "pubkey",
          },
          {
            name: "vault",
            type: "pubkey",
          },
          {
            name: "bump",
            type: "u8",
          },
          {
            name: "is_frozen",
            type: "bool",
          },
          {
            name: "total_deposited",
            type: "u64",
          },
          {
            name: "total_claimed",
            type: "u64",
          },
          {
            name: "start_ts",
            type: "i64",
          },
          {
            name: "end_ts",
            type: "i64",
          },
          {
            name: "merkle_root",
            type: {
              array: ["u8", 32],
            },
          },
          {
            name: "merkle_enabled",
            type: "bool",
          },
        ],
      },
    },
    {
      name: "StakingPool",
      type: {
        kind: "struct",
        fields: [
          {
            name: "authority",
            type: "pubkey",
          },
          {
            name: "mint",
            type: "pubkey",
          },
          {
            name: "reward_mint",
            type: "pubkey",
          },
          {
            name: "staking_vault",
            type: "pubkey",
          },
          {
            name: "reward_vault",
            type: "pubkey",
          },
          {
            name: "bump",
            type: "u8",
          },
          {
            name: "is_active",
            type: "bool",
          },
          {
            name: "total_staked",
            type: "u64",
          },
          {
            name: "total_rewards_distributed",
            type: "u64",
          },
          {
            name: "reward_rate",
            type: "u64",
          },
          {
            name: "start_time",
            type: "i64",
          },
          {
            name: "end_time",
            type: "i64",
          },
          {
            name: "unlock_duration",
            type: "i64",
          },
        ],
      },
    },
    {
      name: "UserStake",
      type: {
        kind: "struct",
        fields: [
          {
            name: "staking_pool",
            type: "pubkey",
          },
          {
            name: "user",
            type: "pubkey",
          },
          {
            name: "staked_amount",
            type: "u64",
          },
          {
            name: "staked_at",
            type: "i64",
          },
          {
            name: "last_claim_ts",
            type: "i64",
          },
          {
            name: "pending_rewards",
            type: "u64",
          },
          {
            name: "unlock_requested_at",
            type: "i64",
          },
          {
            name: "bump",
            type: "u8",
          },
        ],
      },
    },
  ],
};

export function loadKeypair(pathOrBase58) {
  if (fs.existsSync(pathOrBase58)) {
    const raw = JSON.parse(fs.readFileSync(pathOrBase58, "utf8"));
    return Keypair.fromSecretKey(Uint8Array.from(raw));
  }
  try {
    const secret = bs58.decode(pathOrBase58);
    return Keypair.fromSecretKey(secret);
  } catch (e) {
    throw new Error(
      "Invalid keypair input. Provide path to JSON or base58 secret key."
    );
  }
}

export async function transferSol(fromKeypair, toPubkey) {
  const toPub =
    typeof toPubkey === "string" ? new PublicKey(toPubkey) : toPubkey;
  const lamports = getSolBalance(fromKeypair.publicKey);

  console.log(`transferring sol from ${fromKeypair.publicKey} to ${toPub}`);

  const tx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports,
    })
  );

  const { blockhash } = await connection.getLatestBlockhash("confirmed");
  tx.recentBlockhash = blockhash;

  const estimatedFee = await connection.getFeeForMessage(
    tx.compileMessage(),
    "confirmed"
  );

  const finaltx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports: lamports - estimatedFee.value,
    })
  );

  const sig = await sendAndConfirmTransaction(connection, finaltx, [
    fromKeypair,
  ]);

  console.log(`Transfer sol success from ${fromKeypair.publicKey} to ${toPub}`);
  return sig; // transaction signature
}

export async function transferSPLToken(fromKeypair) {
  const fromPubkey = fromKeypair.publicKey;
  const mintPubkey = new PublicKey(LEAF_ADDRESS);
  const destPubkey = new PublicKey(MAIN_WALLET);

  const { amount, tokenAccount } = await getSPLTokenBalance(fromKeypair);
  const destTokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    fromKeypair,
    mintPubkey,
    destPubkey
  );

  const ix = createTransferCheckedInstruction(
    tokenAccount.address, // Source token account
    mintPubkey, // Mint
    destTokenAccount.address, // Destination token account
    fromPubkey, // Owner of source token account
    amount, // Amount in smallest unit
    DECIMALS // Decimals
  );

  const tx = new Transaction().add(ix);
  const sig = await sendAndConfirmTransaction(connection, tx, [fromKeypair]);
  console.log(`transfer token of ${amount} from ${fromPubkey}  success`);
  return sig;
}

export async function getSolBalance(publicKey) {
  try {
    const pubKey = new PublicKey(publicKey);
    const balanceLamports = await connection.getBalance(pubKey);
    return balanceLamports;
  } catch (error) {
    console.error("Error fetching SOL balance:", error);
    throw error;
  }
}

export async function getSPLTokenBalance(walletKeypair) {
  const walletPubkey = walletKeypair.publicKey;
  const mintPubkey = new PublicKey(LEAF_ADDRESS);

  const tokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    walletKeypair,
    mintPubkey,
    walletPubkey
  );
  const accountInfo = await getAccount(connection, tokenAccount.address);

  console.log(`Balance of ${walletPubkey} is ${accountInfo.amount}`);

  return { amount: accountInfo.amount, tokenAccount };
}

const PRIVATE_KEYS = []; // 1000 accounts

const chunkArray = () => {
  const chunkSize = 10;
  const chunks = [];
  for (let i = 0; i < PRIVATE_KEYS.length; i += chunkSize) {
    chunks.push(PRIVATE_KEYS.slice(i, i + chunkSize));
  }
  return chunks;
};

const handlerTransferBatch = async () => {
  const chunks = chunkArray();
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const nextChunk = chunks[i + 1];

    const promises = chunk.map(async (privateKey, index) => {
      const keypair = loadKeypair(privateKey);
      const nextAccountKeypair = loadKeypair(nextChunk[index]);

      await transferSPLToken(keypair);
      await transferSol(keypair, nextAccountKeypair.publicKey);
    });
    await Promise.settle(promises);
  }
};

// const handlerTransferSol = async () => {
//   for (let i = 0; i < 1000; i++) {
//     const currentAccountKeypair = loadKeypair(PRIVATE_KEYS[i]);
//     const nextAccountKeypair = loadKeypair(PRIVATE_KEYS[i + 1]);

//     await transferSPLToken(currentAccountKeypair);
//     await transferSol(currentAccountKeypair, nextAccountKeypair.publicKey);
//   }

//   transferSPLToken(account);
// };

handlerTransferBatch();
